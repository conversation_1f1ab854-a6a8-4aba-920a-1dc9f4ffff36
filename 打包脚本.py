#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
半托找图WEB版 - 打包脚本
自动打包主程序为exe文件，包含所有依赖和资源文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller 已安装")
        return True
    except ImportError:
        print("× PyInstaller 未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"× PyInstaller 安装失败: {e}")
        return False


def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files

# 收集数据文件
datas = []

# 添加templates目录
if os.path.exists('templates'):
    datas.append(('templates', 'templates'))

# 添加settings.ini配置文件
if os.path.exists('settings.ini'):
    datas.append(('settings.ini', '.'))

# 添加key.vdf文件（如果存在）
if os.path.exists('key.vdf'):
    datas.append(('key.vdf', '.'))

# 添加商品数据目录（如果存在）
for item in os.listdir('.'):
    if os.path.isdir(item) and item.startswith('商品数据_'):
        datas.append((item, item))

# 收集Flask模板文件
flask_datas = collect_data_files('flask')
datas.extend(flask_datas)

# 隐藏导入的模块
hiddenimports = [
    'flask',
    'jinja2',
    'werkzeug',
    'requests',
    'PIL',
    'PIL.Image',
    'cryptography',
    'cryptography.hazmat.primitives.kdf.pbkdf2',
    'cryptography.hazmat.primitives.hashes',
    'cryptography.hazmat.primitives.padding',
    'cryptography.hazmat.primitives.ciphers',
    'bs4',
    'pyperclip',
    'configparser',
    'queue',
    'threading',
    'tempfile',
    'zipfile',
    'tkinter',
    'tkinter.messagebox',
    'webbrowser',
    'datetime',
    'urllib.parse',
    'json',
    're',
    'io',
    'time',
]

block_cipher = None

a = Analysis(
    ['主程序.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='半托找图WEB版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('半托找图WEB版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建 spec 文件")


def run_pyinstaller():
    """运行PyInstaller打包"""
    print("开始打包程序...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✓ 清理了旧的build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✓ 清理了旧的dist目录")
    
    # 运行PyInstaller
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '半托找图WEB版.spec'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 打包成功!")
            print("\n打包输出:")
            print(result.stdout)
            
            # 检查exe文件是否生成
            exe_path = os.path.join('dist', '半托找图WEB版.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 生成的exe文件: {exe_path}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("× 未找到生成的exe文件")
                return False
        else:
            print("× 打包失败!")
            print("错误输出:")
            print(result.stderr)
            print("标准输出:")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"× 打包过程中出现异常: {e}")
        return False


def create_requirements_txt():
    """创建requirements.txt文件记录依赖"""
    requirements = [
        "flask>=2.0.0",
        "requests>=2.25.0",
        "Pillow>=8.0.0",
        "cryptography>=3.4.0",
        "beautifulsoup4>=4.9.0",
        "pyperclip>=1.8.0",
        "pyinstaller>=4.5",
    ]
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        for req in requirements:
            f.write(req + '\n')
    
    print("✓ 已创建 requirements.txt 文件")


def cleanup_build_files():
    """清理构建过程中的临时文件"""
    files_to_remove = [
        '半托找图WEB版.spec',
        'requirements.txt'
    ]
    
    dirs_to_remove = [
        'build',
        '__pycache__'
    ]
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✓ 已清理临时文件: {file_name}")
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理临时目录: {dir_name}")


def main():
    """主函数"""
    print("=" * 60)
    print("            半托找图WEB版 - 自动打包脚本")
    print("=" * 60)
    
    # 检查主程序文件是否存在
    if not os.path.exists('主程序.py'):
        print("× 错误: 未找到主程序.py文件")
        print("请确保在正确的目录下运行此脚本")
        input("按回车键退出...")
        return False
    
    print("✓ 找到主程序文件")
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        print("正在尝试安装PyInstaller...")
        if not install_pyinstaller():
            print("× 无法安装PyInstaller，请手动安装:")
            print("  pip install pyinstaller")
            input("按回车键退出...")
            return False
    
    try:
        # 创建requirements.txt
        create_requirements_txt()
        
        # 创建spec文件
        create_spec_file()
        
        # 运行打包
        success = run_pyinstaller()
        
        if success:
            print("\n" + "=" * 60)
            print("                  打包完成!")
            print("=" * 60)
            print(f"✓ 可执行文件位置: dist{os.sep}半托找图WEB版.exe")
            print("✓ 您可以直接运行该exe文件")
            print("✓ 所有依赖和资源文件已包含在内")
            
            # 询问是否清理临时文件
            choice = input("\n是否清理临时文件? (y/n, 默认: y): ").strip().lower()
            if choice in ['', 'y', 'yes']:
                cleanup_build_files()
                print("✓ 临时文件清理完成")
            
            print("\n打包成功! 🎉")
            
        else:
            print("\n× 打包失败，请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n\n× 用户取消了打包过程")
        
    except Exception as e:
        print(f"\n× 打包过程中出现未知错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        input("\n按回车键退出...")
        
    return success


if __name__ == "__main__":
    main()

